package com.bassims.config;

import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.HashMap;
import java.util.Map;

/**
 * Spring Boot Actuator 配置类
 * 确保 Actuator 端点能够正确处理内容协商
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Configuration
public class ActuatorConfig implements WebMvcConfigurer {

    /**
     * 配置内容协商，确保 Actuator 端点能正确返回对应格式
     * 特别是 Prometheus 端点需要返回 text/plain 格式
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer
            .favorParameter(false)  // 不使用参数进行内容协商
            .favorPathExtension(false)  // 不使用路径扩展名
            .ignoreAcceptHeader(false)  // 使用 Accept 头
            .defaultContentType(MediaType.APPLICATION_JSON)  // 默认内容类型
            .mediaType("json", MediaType.APPLICATION_JSON)
            .mediaType("xml", MediaType.APPLICATION_XML)
            .mediaType("txt", MediaType.TEXT_PLAIN)  // 支持 text/plain，用于 Prometheus
            .mediaType("plain", MediaType.TEXT_PLAIN);
    }

    /**
     * 自定义应用信息贡献者
     * 为 /actuator/info 端点提供额外的应用信息
     */
    @Bean
    public InfoContributor customInfoContributor() {
        return builder -> {
            Map<String, Object> details = new HashMap<>();

            // 应用信息
            Map<String, Object> appInfo = new HashMap<>();
            appInfo.put("name", "ACMS Backend Service");
            appInfo.put("description", "Asset Construction Management System Backend");
            appInfo.put("version", "2.6");
            appInfo.put("status", "running");
            details.put("app", appInfo);

            // 系统信息
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("java.version", System.getProperty("java.version"));
            systemInfo.put("java.vendor", System.getProperty("java.vendor"));
            systemInfo.put("os.name", System.getProperty("os.name"));
            systemInfo.put("os.version", System.getProperty("os.version"));
            details.put("system", systemInfo);

            details.put("timestamp", System.currentTimeMillis());

            builder.withDetails(details);
        };
    }
}
