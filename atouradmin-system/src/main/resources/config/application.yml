server:
  port: 8002
#  servlet:
#    context-path: /fcms
app:
  id: acms-backend
application_prefix: local-
spring:
  application:
    name: ${application_prefix}${app.id}
  profiles:
    active:  @profile.active@
  cache:
    type: REDIS
    cache-names: role,user,menu,job,dict,dept,data
#  freemarker:
#    check-template-location: false
#  jackson:
#    time-zone: GMT+8
#  data:
#    redis:
#      repositories:
#        enabled: false
#
#  #配置 Jpa
#  jpa:
#    properties:
#      hibernate:
#        ddl-auto: none
#        dialect: org.hibernate.dialect.MySQL5InnoDBDialect
#    open-in-view: true
#
#
#
#task:
#  pool:
#    # 核心线程池大小
#    core-pool-size: 10
#    # 最大线程数
#    max-pool-size: 30
#    # 活跃时间
#    keep-alive-seconds: 60
#    # 队列容量
#    queue-capacity: 50
#
##七牛云
#qiniu:
#  # 文件大小 /M
#  max-size: 15
#
##邮箱验证码有效时间/秒
#code:
#  expiration: 300
#
##密码加密传输，前端公钥加密，后端私钥解密
#rsa:
#  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

# Eureka Client 配置
eureka:
  client:
    # 是否向注册中心注册自己
    register-with-eureka: true
    # 是否从注册中心获取注册信息
    fetch-registry: true
    # 注册中心地址将在各环境配置文件中指定
  instance:
    # 实例ID
    instance-id: ${spring.application.name}:${server.port}
    # 优先使用IP地址注册
    prefer-ip-address: true
    # 心跳间隔时间（默认30秒）
    lease-renewal-interval-in-seconds: 30
    # 服务失效时间（默认90秒）
    lease-expiration-duration-in-seconds: 90

# Apollo 配置中心
apollo:
  # Apollo应用ID
  appId: ${app.id}
  # Apollo Meta Server地址，将在各环境配置文件中指定
  meta: https://qa-apollo-config-tx.at-our.com
  # 集群名称，默认为default
  cluster: default
  # 是否开启Apollo配置
  bootstrap:
    enabled: true
    # 启用的命名空间，多个用逗号分隔
    namespaces: application

# Spring Boot Actuator 管理端点配置
management:
  endpoints:
    web:
      exposure:
        # 暴露所有端点，包括 prometheus
        include: "*"
      base-path: /actuator
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
